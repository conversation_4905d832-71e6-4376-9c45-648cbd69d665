---
type: "always_apply"
---

# CFA Quant Awards Augmentation Ruleset

This ruleset guides AI tools in assisting with the development of CFA Quantitative Finance Awards submissions, focusing on practical applications, innovation, and professional presentation. Adherence to these rules ensures consistency, clarity, and compliance with CFA award criteria while maximizing competitive advantage.

**IMPORTANT:**
- This project is for writing a CFA award submission in LaTeX.
- **Do NOT** change any code in the `src` directory.
- **Focus** on editing the `.tex` files and the `references.bib` file.
- **Rebuild** the project after editing any `.tex` file.
- **Page limit**: 5-7 pages (excluding appendices) - be ruthless about cutting content.

---

## 1. CFA Award Criteria & Strategic Focus (Most Important)

### 1.1 Judging Criteria (Weighted Priorities)
1. **Applicability and Relevance (30%)**
   - Lead with practical business problems and real-world applications
   - Emphasize value to quantitative finance professionals
   - Show clear implementation potential and industry impact
   - Frame every technical contribution in terms of practitioner benefits

2. **Innovation (30%)**
   - Highlight novel approaches, creative solutions, or unique insights
   - Position work as solving existing industry problems
   - Emphasize methodological advances that enable new capabilities
   - Show how your approach differs from and improves upon existing methods

3. **Accuracy and Completeness (30%)**
   - Ensure methodological soundness with proper validation
   - Provide complete analysis with quantified performance improvements
   - Include honest discussion of limitations and assumptions
   - Support all claims with rigorous evidence

4. **Presentation (10%)**
   - Maintain clear, professional writing throughout
   - Use effective tables and figures that support the narrative
   - Ensure logical flow and organization
   - Follow proper academic formatting standards

### 1.2 Content Strategy
- **Problem-Solution Narrative**: Structure as compelling story from practitioner pain point to solution
- **Quantified Value**: Use specific performance improvements (e.g., "30% improvement in volatility tracking")
- **Implementation Focus**: Emphasize deployability, computational efficiency, and practical constraints
- **Hybrid Insights**: Frame limitations as opportunities for improved modeling approaches

---

## 2. Writing Style & Narrative Structure

### 2.1 Voice & Tone
- **Professional Practitioner Focus**: Write for quantitative finance professionals, not academics
- **Confident but Measured**: Assert value while acknowledging limitations honestly
- **Action-Oriented**: Use active voice and emphasize practical outcomes
- **Business-Minded**: Frame technical contributions in terms of business value

### 2.2 Narrative Flow
- **Lead with Business Value**: Start each section with practical motivation
- **Build Logical Arguments**: Structure as problem → solution → validation → insight
- **Eliminate Bullet Points**: Convert all lists into flowing prose narrative
- **Strong Transitions**: Use transitional sentences to connect sections seamlessly
- **Quantify Benefits**: Support claims with specific, measurable improvements

### 2.3 Section Integration Strategy
- **Merge Empirical Applications and Results**: Combine into single narrative showing decomposition → diagnostics → insights
- **Use Strong Transitional Sentences**: Connect methodology to applications to conclusions
- **Frame Sections as Problem-Solution Comparisons**: Rather than separate descriptions
- **Cut Ruthlessly**: Stay within 5-7 page limit by eliminating redundancy

---

## 3. Content Structure & Key Sections

### 3.1 Title & Abstract
- **Title**: Informative, emphasizing practical value over technique names
- **Abstract (200-250 words)**:
  - State central business problem and practical contribution
  - Highlight main quantified findings and business implications
  - Do **not** cite literature in abstract

### 3.2 Introduction (~1 page)
- **Opening**: Begin directly with practitioner problem and business impact
- **Structure**:
  1. Business problem and current trade-offs
  2. Your solution and core innovation
  3. Value demonstration and quantified benefits
  4. Implementation availability (open-source package)
- **Avoid**: Separate literature review or methodology roadmap sections

### 3.3 Methodology Section (~2 pages)
- **Focus**: Frame as "solving the stability problem" rather than technical exposition
- **Structure**:
  1. Brief model explanation emphasizing business value
  2. Core contribution: stability and accuracy improvements
  3. Quantified performance comparisons
  4. Computational advantages
- **Emphasis**: Superior performance metrics and practical reliability

### 3.4 Applications & Results (~2 pages)
- **Integrated Narrative**: Merge empirical applications and results into single story
- **Structure**:
  1. Risk decomposition capabilities with real-world portfolio
  2. Diagnostic insights enabled by stable estimation
  3. Practical implications and hybrid modeling recommendations
- **Focus**: What practitioners can do with reliable estimation that they couldn't before

### 3.5 Conclusion (~1 page)
- **Restate Problem**: Practitioner challenges with existing methods
- **Summarize Solution**: Stable framework with quantified improvements
- **Highlight Payoffs**: Risk decomposition and diagnostic insights
- **Practical Guidance**: When to use, limitations, and implementation recommendations

### 3.6 Appendices (Not counted toward page limit)
- **Open-source implementation details**
- **Extended technical results**
- **Mathematical derivations (minimal)**

---

## 4. Mathematical Notation & Technical Content

### 4.1 Model Presentation
- **Complete DFSV Specification**: Include all three key equations (returns, factors, volatility)
- **Intuitive Explanations**: Frame equations in terms of business concepts
- **Practical Implications**: Connect each component to risk management applications

### 4.2 Performance Metrics
- **Quantified Improvements**: "30% reduction in volatility tracking error"
- **Stability Measures**: Mean-to-median ratios showing filter reliability
- **Computational Efficiency**: O(NK²) complexity advantages
- **Success Rates**: 100% estimation success vs. variable particle filter performance

### 4.3 Technical Depth
- **Sufficient Detail**: Enough for replication without overwhelming practitioners
- **Focus on Advantages**: Emphasize why your approach is better
- **Honest Limitations**: Gaussian assumptions, computational requirements, tail risk limitations

---

## 5. Figures, Tables & Presentation

### 5.1 Visual Strategy
- **Targeted Figures**: Each figure must directly support the narrative
- **Self-Contained Captions**: Figures should be interpretable independently
- **Professional Formatting**: Use `booktabs` for tables, consistent styling

### 5.2 Key Visualizations
- **Performance Comparisons**: Volatility tracking error, stability ratios
- **Risk Decomposition**: Latent factors during crisis periods
- **Diagnostic Results**: Model specification tests and pass rates
- **Computational Scaling**: Runtime vs. dimensionality

### 5.3 Table Guidelines
- **Quantified Results**: Specific performance improvements
- **Clear Comparisons**: Your method vs. benchmarks
- **Practical Metrics**: Measures that matter to practitioners
- **Appropriate Precision**: Meaningful significant figures

---

## 6. LaTeX Implementation & Formatting

### 6.1 Document Structure
- **Class**: `\documentclass[11pt]{article}` for professional appearance
- **Packages**: Include `amsmath`, `booktabs`, `graphicx`, `hyperref`
- **Page Limits**: Strict 5-7 page enforcement

### 6.2 Section Organization
- **Integrated Sections**: Avoid excessive subsectioning
- **Flowing Narrative**: Eliminate bullet points and lists
- **Professional Headings**: Clear, descriptive section titles

### 6.3 Citations & References
- **Industry Relevance**: Cite practitioner-relevant sources
- **Recent Literature**: Focus on current developments
- **Proper Attribution**: Academic integrity while maintaining flow

---

## 7. Competitive Advantage Strategy

### 7.1 Differentiation Points
- **Stability Solution**: First practical BIF application to DFSV models
- **Quantified Performance**: Specific, measurable improvements
- **Open-Source Implementation**: Deployable Python package
- **Diagnostic Insights**: Model specification discoveries
- **Hybrid Recommendations**: Practical modeling guidance

### 7.2 Value Proposition
- **Solves Real Problems**: Numerical instability in high-dimensional models
- **Enables New Capabilities**: Reliable risk decomposition and diagnostics
- **Production-Ready**: Deterministic, scalable, tested implementation
- **Actionable Insights**: Clear guidance for practitioners

### 7.3 Honest Positioning
- **Clear Limitations**: Gaussian assumptions, computational intensity
- **Appropriate Use Cases**: When to use vs. simpler alternatives
- **Implementation Guidance**: Practical deployment considerations

---

## 8. Final Success Factors

### 8.1 Content Quality
- **Evidence-Based Claims**: Support every statement with data
- **Practitioner Lens**: Always ask "How does this help risk managers?"
- **Clear Value Proposition**: Business case obvious from first paragraph
- **Diagnostic Insight**: Frame model limitations as valuable discoveries

### 8.2 Presentation Excellence
- **Professional Writing**: Measured, confident tone throughout
- **Logical Flow**: Seamless progression from problem to solution to insight
- **Effective Visuals**: Figures and tables that enhance understanding
- **Proper Formatting**: Clean, consistent LaTeX presentation

### 8.3 Competitive Edge
- **Innovation Emphasis**: Novel application of BIF to DFSV models
- **Practical Impact**: Real-world deployment and business value
- **Complete Solution**: From theory to implementation to insights
- **Industry Relevance**: Addresses current practitioner challenges

---

## 9. Submission Requirements

### 9.1 Format Compliance
- **Page Limit**: 5-7 pages (excluding appendices)
- **Anonymous Submission**: No author names in main document
- **PDF Format**: Professional LaTeX compilation
- **File Naming**: "Quant Awards - [Name] - [Institution].pdf"

### 9.2 Content Checklist
- **Practical Focus**: Emphasizes applications over techniques
- **Innovation Highlight**: Clear novel contributions
- **Complete Analysis**: Proper validation and limitations
- **Professional Presentation**: Clear writing and effective visuals

### 9.3 Final Review
- **Practitioner Value**: Every section contributes to business case
- **Narrative Flow**: Seamless story from problem to solution
- **Quantified Benefits**: Specific, measurable improvements
- **Implementation Ready**: Clear path to practical deployment
